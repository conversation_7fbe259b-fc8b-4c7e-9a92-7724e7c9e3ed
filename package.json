{"name": "xyz.chatboxapp.ce", "productName": "xyz.chatboxapp.ce", "version": "0.0.1", "private": true, "description": "A desktop client for multiple cutting-edge AI models", "main": "./src/main/main.ts", "scripts": {"build": "concurrently \"npm run build:main\" \"npm run build:renderer\"", "build:main": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.main.prod.ts", "build:renderer": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.renderer.prod.ts", "build:web": "cross-env CHATBOX_BUILD_PLATFORM=web npm run build:renderer", "postinstall": "ts-node .erb/scripts/check-native-dep.js && electron-builder install-app-deps && cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.renderer.dev.dll.ts", "package": "ts-node ./.erb/scripts/clean.js dist && npm run build && electron-builder build --publish never", "package:all": "ts-node ./.erb/scripts/clean.js dist && npm run build && electron-builder build --publish never --win --mac --linux", "release:web": "bash release-web.sh", "release:mac": "bash release-mac.sh", "release:linux": "bash release-linux.sh", "release:win": "bash release-win.sh", "electron:publish-mac": "ts-node ./.erb/scripts/clean.js dist && npm install && npm run postinstall && npm run build && electron-builder build --publish always --mac", "electron:publish-linux": "ts-node ./.erb/scripts/clean.js dist && npm install && npm run postinstall && npm run build && electron-builder build --publish always --linux", "electron:publish-win": "ts-node ./.erb/scripts/clean.js dist && npm install && npm run postinstall && npm run build && electron-builder build --publish always --win", "rebuild": "electron-rebuild --parallel --types prod,dev,optional --module-dir release/app", "dev": "npm start", "dev:web": "cross-env DEV_WEB_ONLY=true CHATBOX_BUILD_PLATFORM=web npm start", "start": "ts-node ./.erb/scripts/check-port-in-use.js && npm run start:renderer", "start:main": "cross-env NODE_ENV=development electronmon -r ts-node/register/transpile-only .", "start:preload": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true webpack --config ./.erb/configs/webpack.config.preload.dev.ts", "start:renderer": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true webpack serve --config ./.erb/configs/webpack.config.renderer.dev.ts", "serve:web": "npx serve ./release/app/dist/renderer", "test": "jest", "lint": "cross-env NODE_ENV=development eslint . --ext .js,.jsx,.ts,.tsx", "check": "npx tsc --noEmit", "prettier": "prettier -w \"./src/**/*.{ts,tsx,json,css}\"", "mobile:sync": "npm run mobile:sync:ios && npm run mobile:sync:android", "mobile:sync:ios": "ts-node ./.erb/scripts/clean.js dist && cross-env CHATBOX_BUILD_TARGET=mobile_app CHATBOX_BUILD_PLATFORM=ios npm run build:renderer && npm run delete-sourcemaps && npx cap sync ios", "mobile:sync:android": "ts-node ./.erb/scripts/clean.js dist && cross-env CHATBOX_BUILD_TARGET=mobile_app CHATBOX_BUILD_PLATFORM=android npm run build:renderer && npm run delete-sourcemaps && npx cap sync android", "mobile:ios": "npm run mobile:sync:ios && npx cap open ios", "mobile:android": "npm run mobile:sync:android && npx cap open android", "mobile:assets": "npx capacitor-assets generate --ios --android", "prepare": "husky", "translate": "i18next && node script/translate.mjs"}, "repository": {"type": "git", "url": "https://github.com/chatboxai/chatbox.git"}, "keywords": [], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^6.0.0", "@electron/notarize": "^2.0.0", "@electron/rebuild": "^3.2.13", "@emotion/babel-plugin": "^11.13.5", "@emotion/babel-preset-css-prop": "^11.12.0", "@faker-js/faker": "^8.0.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.10", "@svgr/webpack": "^8.0.1", "@tanstack/router-plugin": "^1.120.15", "@teamsupercell/typings-for-css-modules-loader": "^2.5.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/auto-launch": "^5.0.5", "@types/autosize": "^4.0.3", "@types/d3": "^7.4.3", "@types/gtag.js": "^0.0.13", "@types/highlight.js": "^10.1.0", "@types/jest": "^29.5.2", "@types/katex": "^0.16.2", "@types/lodash": "^4.14.197", "@types/mark.js": "^8.11.12", "@types/markdown-it": "^12.2.3", "@types/markdown-it-link-attributes": "^3.0.1", "@types/node": "20.2.5", "@types/react": "^18.2.8", "@types/react-dom": "^18.2.4", "@types/react-syntax-highlighter": "^15.5.9", "@types/react-test-renderer": "^18.0.0", "@types/shell-quote": "^1.7.5", "@types/store": "^2.0.2", "@types/terser-webpack-plugin": "^5.0.4", "@types/uuid": "^9.0.1", "@types/webpack-bundle-analyzer": "^4.6.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "autoprefixer": "^10.4.14", "browserslist-config-erb": "^0.0.3", "chalk": "^4.1.2", "concurrently": "^8.1.0", "core-js": "^3.34.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.0", "detect-port": "^1.5.1", "dotenv": "^16.3.1", "electron": "^26.6.10", "electron-builder": "^24.2.1", "electron-devtools-installer": "^3.2.0", "electronmon": "^2.0.2", "eslint": "^8.42.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-erb": "^4.0.6", "eslint-import-resolver-typescript": "^3.5.5", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-compat": "^4.1.4", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^7.2.13", "html-webpack-plugin": "^5.5.1", "husky": "^9.0.11", "i18next-parser": "^9.3.0", "identity-obj-proxy": "^3.0.0", "javascript-obfuscator": "^4.0.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "mini-css-extract-plugin": "^2.7.6", "node-loader": "^2.0.0", "postcss": "^8.5.3", "postcss-loader": "^7.3.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^2.8.8", "react-refresh": "^0.14.0", "react-test-renderer": "^18.2.0", "rimraf": "^5.0.1", "sass": "^1.89.2", "sass-loader": "^16.0.5", "style-loader": "^3.3.3", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.0", "terser-webpack-plugin": "^5.3.9", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.8.3", "url-loader": "^4.1.1", "webpack": "^5.85.0", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0", "webpack-merge": "^5.9.0", "webpack-obfuscator": "^3.5.1"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/azure": "^1.3.13", "@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.12", "@ai-sdk/openai-compatible": "^0.2.14", "@ai-sdk/perplexity": "^1.1.7", "@braintree/sanitize-url": "^6.0.4", "@capacitor-community/sqlite": "^6.0.2", "@capacitor/app": "^6.0.2", "@capacitor/core": "^6.1.1", "@capacitor/filesystem": "^6.0.0", "@capacitor/keyboard": "^6.0.2", "@capacitor/share": "^6.0.1", "@capacitor/toast": "^6.0.1", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@ebay/nice-modal-react": "^1.2.13", "@emotion/css": "^11.13.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@epic-web/cachified": "^5.5.1", "@mantine/core": "^7.17.7", "@mantine/form": "^7.17.7", "@mantine/hooks": "^7.17.7", "@mantine/modals": "^7.17.7", "@mantine/spotlight": "^7.17.7", "@modelcontextprotocol/sdk": "^1.12.1", "@mozilla/readability": "^0.5.0", "@mui/icons-material": "^5.11.11", "@mui/material": "^5.11.11", "@radix-ui/react-dialog": "^1.0.5", "@sentry/react": "^7.73.0", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-router": "^1.114.23", "@tanstack/router-devtools": "^1.114.23", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "ai": "^4.3.15", "auto-launch": "^5.0.6", "autosize": "^6.0.1", "axios": "^1.3.4", "capacitor-plugin-safe-area": "^3.0.3", "chardet": "^2.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "copy-to-clipboard": "^3.3.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "electron-debug": "^3.2.0", "electron-log": "^5.3.4", "electron-store": "^8.1.0", "electron-updater": "^6.3.9", "emittery": "^1.1.0", "form-data": "^4.0.0", "fs-extra": "^11.1.1", "highlight.js": "^11.7.0", "i18next": "^22.4.13", "iconv-lite": "^0.6.3", "immer": "^10.1.1", "jotai": "^2.1.0", "jotai-immer": "^0.4.1", "jotai-optics": "^0.3.0", "js-tiktoken": "^1.0.7", "katex": "^0.16.22", "linkedom": "^0.18.5", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.419.0", "mark.js": "^8.11.1", "material-ui-popup-state": "^5.0.4", "mermaid": "^11.4.0", "ofetch": "^1.0.1", "officeparser": "^5.0.0", "optics-ts": "^2.4.0", "p-map": "^7.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^12.2.0", "react-markdown": "^9.0.0", "react-router-dom": "^6.11.2", "react-syntax-highlighter": "^15.5.0", "react-virtuoso": "^4.10.4", "react-zoom-pan-pinch": "^3.4.4", "rehype-katex": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sanitize-filename": "^1.6.3", "shell-env": "^4.0.1", "shell-quote": "^1.8.2", "sonner": "^2.0.3", "store": "^2.0.12", "swr": "^2.1.5", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "browserslist": [], "electronmon": {"patterns": ["!**/**", "src/main/**"], "logLevel": "quiet"}}