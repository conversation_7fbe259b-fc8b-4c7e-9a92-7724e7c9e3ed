<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <!-- <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' *.example.com"
    /> -->
    <meta
      name="viewport"
      content="height=device-height, width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="chatbox" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="chatbox" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <!-- <link rel="manifest" href="%PUBLIC_URL%/manifest.json" /> -->
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Chatbox</title>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-B365F44W6E"></script>
    <script defer data-domain="app.chatboxai.app" src="https://plausible.midway.run/js/script.local.hash.js"></script>
    <script>
      window.plausible =
        window.plausible ||
        function () {
          ;(window.plausible.q = window.plausible.q || []).push(arguments)
        }
    </script>

    <script>
      window.dataLayer = window.dataLayer || []
      function gtag() {
        dataLayer.push(arguments)
      }
      gtag('js', new Date())

      // gtag('config', 'G-B365F44W6E');
    </script>
    <script>
      var initialTheme = localStorage.getItem('initial-theme')
      if (initialTheme === 'light' || initialTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', initialTheme)
        document.documentElement.setAttribute('data-mantine-color-scheme', initialTheme)
      }
    </script>
    <style>
      html[data-theme='dark'] {
        background-color: #242424;
      }
      .loading-text-color {
        color: #333;
      }
      html[data-theme='dark'] .loading-text-color {
        color: #fff;
      }
    </style>
  </head>

  <body>
    <div id="root">
      <div style="height: 80vh; display: flex; flex-direction: column; justify-content: center; align-items: center">
        <h1 style="font-family: 'Roboto', sans-serif; font-weight: 700; margin: 0" class="loading-text-color">
          Chatbox
        </h1>
        <p style="font-family: 'Roboto', sans-serif; font-weight: 400; opacity: 0.4" class="loading-text-color">
          loading...
        </p>
      </div>
    </div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
